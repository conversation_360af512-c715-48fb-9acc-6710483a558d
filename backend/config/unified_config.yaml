business_rules:
  action_handlers:
    handler_classes:
      CompositeHandler: backend.handlers.composite_handler.CompositeHandler
      ConversationHandler: backend.handlers.conversation_handler.ConversationHandler
      DocumentHandler: backend.handlers.document_handler.DocumentHandler
      GeneralRequestHandler: backend.handlers.general_request_handler.GeneralRequestHandler
      KnowledgeBaseHandler: backend.handlers.knowledge_base_handler.KnowledgeBaseHandler
      RequirementHandler: backend.handlers.requirement_handler.RequirementHandler
  document_confirmation:
    confirmation_keywords:
    - 确认
    - 没问题
    - 正确
    - 同意
    - 确认无误
    - 批准
    - 好的
    - ok
    - okay
    - confirm
    - 'yes'
    - good
  focus_point_priority:
    p0: true
    p1: true
    p2: true
  quality_control:
    max_input_length: 1000
    min_input_length: 2
    spam_detection_enabled: true
  requirement_collection:
    completion_threshold: 0.8
    max_focus_points: 10
    min_focus_points: 3
  retry:
    backoff_factor: 1.5
    max_pending_attempts: 3
    max_total_attempts: 5
business_templates:
  acknowledge_and_redirect:
    fallback_template: '好的，请告诉我您的需求，我来帮您整理。

      '
    main_template: '好的，我明白了。让我们回到需求收集的主题上。请告诉我您希望整理哪些业务需求，或者您想从哪个方面开始？

      '
    simplified_template: '明白了。让我们专注于您的需求收集工作，请告诉我需要什么帮助？

      '
  empathy_and_clarify:
    fallback_template: '请告诉我您的具体需求，我会尽力帮助您。

      '
    main_template: '我理解您的情况。为了更好地帮助您，能否请您详细说明一下具体的需求或遇到的问题？这样我就能为您提供更精准的支持。

      '
    simplified_template: '我理解您的需要。请告诉我更多详情，这样我就能更好地协助您。

      '
  general_chat:
    fallback_template: '您好！我可以帮您整理业务需求，请告诉我需要什么帮助？

      '
    main_template: '感谢您与我交流！我是专业的需求采集助手。如果您有任何业务需求需要整理，或者想了解需求分析的相关问题，我很乐意为您提供帮助。

      '
    simplified_template: '很高兴与您交流！有什么我可以帮助您的吗？

      '
  rephrase_and_inquire:
    fallback_template: '让我换个角度理解您的需求，请用您最熟悉的方式描述一下这个项目的核心想法。

      '
    main_template: '让我重新理解一下您的需求：


      您提到的 "{message_preview}" 这个想法很有意思。


      **🔄 让我换个方式问：**

      1. 从用户的角度看，这个项目要解决什么痛点？

      2. 如果项目成功了，最直观的改变是什么？

      3. 您觉得这个项目的核心价值在哪里？


      **🎯 具体场景**

      - 能否举个具体的使用场景？

      - 典型的用户会在什么情况下需要这个？


      这样的表述是否更清楚一些？

      '
    simplified_template: '让我换个角度理解您的需求："{message_preview}"


      请用您最熟悉的方式描述一下这个项目的核心想法。

      '
  reset_conversation:
    error_template: '重置过程中遇到了一些问题，但我们可以重新开始。请告诉我您的新需求。

      '
    main_template: '好的，我们重新开始！


      我是AI需求采集助手，可以帮助您：

      🎯 整理和分析业务需求

      📋 制定详细的需求规格

      📝 生成规范的需求文档


      请告诉我您想要讨论的项目或需求，我们开始吧！

      '
    simplified_template: '好的，我们重新开始！请告诉我您想要讨论的项目或需求。

      '
conversation:
  keyword_acceleration:
    enabled: false
    rules:
      ask_question:
        intent: ask_question
        keywords:
        - 你能做什么
        - 有什么功能
        - 能帮我什么
      business_requirement:
        intent: business_requirement
        keywords:
        - 我想做
        - 我需要
        - 帮我做
        - 制作
      confirm:
        intent: confirm
        keywords:
        - 确认
        - 没问题
        - 正确
        - 同意
        - 好的
        - ok
      emotional_support:
        intent: emotional_support
        keywords:
        - 心情不好
        - 安慰我
        - 难过
        - 沮丧
        - 不开心
        - 郁闷
      general_chat:
        intent: general_chat
        keywords:
        - 聊天
        - 闲聊
        - 随便聊聊
      greeting:
        intent: greeting
        keywords:
        - 你好
        - hello
        - hi
        - 您好
  states:
    available:
    - IDLE
    - PROCESSING_INTENT
    - COLLECTING_INFO
    - DOCUMENTING
    - COMPLETED
    - DOMAIN_CLARIFICATION
    - CATEGORY_CLARIFICATION
    - DIRECT_SELECTION
    default: IDLE
  transitions:
    CATEGORY_CLARIFICATION:
      clarification_failed: DIRECT_SELECTION
      clarification_success: COLLECTING_INFO
      restart: IDLE
    COLLECTING_INFO:
      business_requirement: COLLECTING_INFO
      confirm: DOCUMENTING
      provide_information: COLLECTING_INFO
    DIRECT_SELECTION:
      restart: IDLE
      selection_made: COLLECTING_INFO
    DOCUMENTING:
      confirm: IDLE
      modify: DOCUMENTING
      restart: IDLE
    DOMAIN_CLARIFICATION:
      clarification_failed: DIRECT_SELECTION
      clarification_success: COLLECTING_INFO
      restart: IDLE
    IDLE:
      ask_question: IDLE
      business_requirement: COLLECTING_INFO
      domain_classification_failed: DOMAIN_CLARIFICATION
      greeting: IDLE
database:
  connection:
    check_same_thread: false
    path: backend/data/aidatabase.db
    timeout: 30
  queries:
    backup:
      export_conversation: "SELECT c.conversation_id, c.user_id, c.domain_id, c.category_id,\
        \ c.status, c.created_at, c.updated_at,\n       GROUP_CONCAT(m.content, '|||')\
        \ as messages,\n       GROUP_CONCAT(d.content, '|||') as documents\nFROM conversations\
        \ c\nLEFT JOIN messages m ON c.conversation_id = m.conversation_id AND c.user_id\
        \ = m.user_id\nLEFT JOIN documents d ON c.conversation_id = d.conversation_id\
        \ AND c.user_id = d.user_id\nWHERE c.conversation_id = ? AND c.user_id = ?\n\
        GROUP BY c.conversation_id, c.user_id\n"
    batch_size: 100
    concern_point_coverage:
      get_by_conversation: 'SELECT coverage_id, conversation_id, user_id, focus_id,
        status, attempts, is_covered, extracted_info, updated_at, additional_data

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ?

        ORDER BY updated_at ASC

        '
      get_coverage_by_id: 'SELECT coverage_id, conversation_id, user_id, focus_id,
        status, attempts, is_covered, extracted_info, updated_at, additional_data

        FROM concern_point_coverage

        WHERE coverage_id = ? AND user_id = ?

        '
      get_processing_points: 'SELECT coverage_id, conversation_id, user_id, focus_id,
        status, attempts, is_covered, extracted_info, updated_at, additional_data

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ? AND status = ''processing''

        ORDER BY updated_at ASC

        '
      insert_coverage: 'INSERT INTO concern_point_coverage (conversation_id, user_id,
        focus_id, status, attempts, is_covered, extracted_info, updated_at, additional_data)

        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)

        '
      update_status: 'UPDATE concern_point_coverage

        SET status = ?, updated_at = ?

        WHERE coverage_id = ? AND user_id = ?

        '
    conversations:
      check_exists: 'SELECT 1 FROM conversations

        WHERE conversation_id = ? AND user_id = ?

        LIMIT 1

        '
      create_new: 'INSERT OR IGNORE INTO conversations (conversation_id, user_id,
        status, created_at, updated_at, last_activity_at)

        VALUES (?, ?, ?, ?, ?, ?)

        '
      delete_expired: 'DELETE FROM conversations

        WHERE last_activity_at < ? AND user_id = ?

        '
      get_active: 'SELECT conversation_id, user_id, domain_id, category_id, status,
        created_at, updated_at, last_activity_at

        FROM conversations

        WHERE last_activity_at > ? AND user_id = ?

        ORDER BY last_activity_at DESC

        '
      get_domain_category: 'SELECT domain_id, category_id

        FROM conversations

        WHERE conversation_id = ? AND user_id = ?

        '
      get_expired: 'SELECT conversation_id, user_id, domain_id, category_id, status,
        created_at, updated_at, last_activity_at

        FROM conversations

        WHERE last_activity_at < ? AND user_id = ?

        ORDER BY last_activity_at ASC

        '
      get_info: 'SELECT conversation_id, user_id, domain_id, category_id, status,
        created_at, updated_at, last_activity_at

        FROM conversations

        WHERE conversation_id = ? AND user_id = ?

        '
      update_last_activity: 'UPDATE conversations

        SET updated_at = ?, last_activity_at = ?

        WHERE conversation_id = ? AND user_id = ?

        '
    documents:
      check_exists: 'SELECT 1 FROM documents

        WHERE document_id = ? AND user_id = ?

        LIMIT 1

        '
      delete_document: 'DELETE FROM documents

        WHERE document_id = ? AND user_id = ?

        '
      get_by_conversation: 'SELECT document_id, conversation_id, user_id, version,
        content, status, feedback, created_at, updated_at

        FROM documents

        WHERE conversation_id = ? AND user_id = ?

        ORDER BY version DESC

        '
      get_content: 'SELECT document_id, conversation_id, user_id, version, content,
        status, feedback, created_at, updated_at

        FROM documents

        WHERE document_id = ? AND user_id = ?

        '
      get_list: 'SELECT document_id, conversation_id, user_id, version, status, created_at,
        updated_at

        FROM documents

        WHERE user_id = ?

        ORDER BY updated_at DESC

        LIMIT ?

        '
      save_document: 'INSERT INTO documents (document_id, conversation_id, user_id,
        version, content, status, created_at, updated_at)

        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

        '
      update_content: 'UPDATE documents

        SET content = ?, status = ?, updated_at = ?

        WHERE document_id = ? AND user_id = ?

        '
      update_status: 'UPDATE documents

        SET status = ?, updated_at = ?

        WHERE document_id = ? AND user_id = ?

        '
    focus_point_definitions:
      get_by_category: 'SELECT focus_id, category_id, name, description, priority,
        example, required

        FROM focus_point_definitions

        WHERE category_id = ?

        ORDER BY priority ASC

        '
      get_by_focus_id: 'SELECT focus_id, category_id, name, description, priority,
        example, required

        FROM focus_point_definitions

        WHERE focus_id = ?

        '
    focus_points:
      batch_insert: 'INSERT INTO concern_point_coverage (conversation_id, user_id,
        focus_id, status, attempts, is_covered, extracted_info, updated_at)

        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

        '
      check_exists: 'SELECT 1 FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        LIMIT 1

        '
      clear_processing: 'UPDATE concern_point_coverage

        SET status = ''pending'', updated_at = datetime(''now'')

        WHERE conversation_id = ? AND user_id = ? AND status = ''processing''

        '
      complex_update: 'UPDATE concern_point_coverage

        SET status = ?, is_covered = ?, extracted_info = ?, attempts = attempts +
        1, updated_at = ?

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        '
      get_completed: 'SELECT focus_id, status, attempts, extracted_info, is_covered,
        updated_at

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ? AND status = ''completed''

        '
      get_single_status: 'SELECT focus_id, status, attempts, extracted_info, is_covered,
        updated_at

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        '
      get_status: 'SELECT focus_id, status, attempts, extracted_info, is_covered,
        updated_at

        FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ?

        '
      insert_new: 'INSERT INTO concern_point_coverage (conversation_id, user_id, focus_id,
        status, attempts, is_covered, extracted_info, updated_at)

        VALUES (?, ?, ?, ?, ?, ?, ?, ?)

        '
      reset_all_status: 'UPDATE concern_point_coverage

        SET status = ''pending'', updated_at = ?

        WHERE conversation_id = ? AND user_id = ?

        '
      reset_status: 'DELETE FROM concern_point_coverage

        WHERE conversation_id = ? AND user_id = ?

        '
      update_status: 'UPDATE concern_point_coverage

        SET status = ?, updated_at = ?

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        '
    max_results: 1000
    messages:
      delete_conversation_messages: 'DELETE FROM messages

        WHERE conversation_id = ? AND user_id = ?

        '
      get_conversation_history_limited: 'SELECT conversation_id, user_id, sender_type,
        content, focus_id, message_type, created_at as timestamp

        FROM messages

        WHERE conversation_id = ? AND user_id = ? AND (message_type IS NULL OR message_type
        != ''summary'')

        ORDER BY created_at ASC

        LIMIT ?

        '
      get_first_user_message: 'SELECT content

        FROM messages

        WHERE conversation_id = ? AND user_id = ? AND sender_type = ''user''

        ORDER BY created_at ASC

        LIMIT 1

        '
      get_messages_by_focus: 'SELECT conversation_id, user_id, sender_type, content,
        focus_id, message_type, created_at

        FROM messages

        WHERE conversation_id = ? AND user_id = ? AND focus_id = ?

        ORDER BY created_at ASC

        '
      get_recent_messages: 'SELECT conversation_id, user_id, sender_type, content,
        focus_id, message_type, created_at

        FROM messages

        WHERE conversation_id = ? AND user_id = ?

        ORDER BY created_at DESC

        LIMIT ?

        '
      save_message: 'INSERT INTO messages (conversation_id, user_id, sender_type,
        content, focus_id, message_type, created_at)

        VALUES (?, ?, ?, ?, ?, ?, ?)

        '
    sessions:
      ensure_session_exists: 'INSERT OR IGNORE INTO sessions (session_id, user_id,
        created_at, updated_at, status)

        VALUES (?, ?, ?, ?, ''active'')

        '
      get_session: 'SELECT session_id, user_id, status, created_at, updated_at

        FROM sessions

        WHERE session_id = ? AND user_id = ?

        '
      update_session: 'UPDATE sessions

        SET updated_at = ?, status = ?

        WHERE session_id = ? AND user_id = ?

        '
    summaries:
      get_summary: 'SELECT summary_json

        FROM conversation_summaries

        WHERE conversation_id = ? AND user_id = ?

        '
      upsert_summary: 'INSERT OR REPLACE INTO conversation_summaries (conversation_id,
        user_id, summary_json, updated_at)

        VALUES (?, ?, ?, datetime(''now''))

        '
  tables:
    conversations:
      auto_cleanup: true
      cleanup_days: 30
    documents:
      auto_backup: true
      backup_interval: 24
    focus_points:
      max_per_conversation: 20
development:
  debug:
    log_requests: false
    log_responses: false
    mock_llm: false
  testing:
    mock_external_apis: false
    use_test_db: false
direct_domain_selection:
  fallback_template: '让我直接帮您收集需求信息。请详细描述一下您的项目需求和期望目标。

    '
  main_template: '我理解您的需求可能比较特殊或复杂。为了确保为您提供最合适的服务，请直接从以下选项中选择最接近的领域：


    **请回复对应的数字或直接说出领域名称：**


    1️⃣ **平面设计** - Logo设计、海报制作、品牌视觉、宣传材料等

    2️⃣ **UI/UX设计** - 界面设计、用户体验、交互设计、原型制作等

    3️⃣ **营销推广** - 活动策划、广告投放、品牌推广、市场营销等

    4️⃣ **法律咨询** - 合同审查、知识产权、法律风险评估、诉讼咨询等

    5️⃣ **软件开发** - 网站建设、APP开发、小程序、系统开发等


    **或者您可以：**

    • 详细描述您的具体需求，我会重新为您分析

    • 说明您的项目目标和期望效果


    请选择最合适的选项，我会立即为您提供专业的需求分析服务。

    '
  simplified_template: '请从以下领域中选择最接近您需求的选项：


    1. 平面设计  2. UI/UX设计  3. 营销推广  4. 法律咨询  5. 软件开发


    请回复数字或领域名称，我会为您提供专业的需求分析。

    '
domain_selection_mapping:
  business:
    extraction:
      point_detail: '  [{index}] {point_name}: {point_value} (完整度: {completeness})'
    focus_points:
      all_completed: 所有关注点已完成采集，开始生成文档
      empty_list: 关注点列表为空
      found_next: '找到下一个待处理的关注点: {point_id} - {point_name}'
      generation_failed: '生成后续问题失败: {error}'
      progress_awareness:
        half_complete: '太棒了！我们已经完成了一半的需求收集 🎉


          ✅ **已完成**：{completed_count}/{total_count} 个关注点

          🎯 **当前重点**：{current_point}


          💡 **专业建议**：基于目前收集的信息，我发现您的项目有很好的可行性。继续保持这个节奏！

          '
        nearly_complete: '太好了！我们即将完成所有需求收集 🎯


          ✅ **已收集**：{completed_count} 个关注点

          🔚 **最后一步**：{current_point}


          💡 **即将完成**：回答完这个问题后，我就可以为您生成完整的需求文档了！

          '
        quarter_complete: '很好！我们已经完成了 25% 的需求收集 📊


          ✅ **已收集信息**：{completed_points}

          🔄 **进行中**：{current_point}

          ⏳ **待收集**：{remaining_points}


          💡 **小贴士**：您回答得很详细，这将帮助我生成更准确的需求文档。

          '
        three_quarters_complete: '非常棒！我们已经完成了 75% 的需求收集 🚀


          ✅ **几乎完成**：只剩下 {remaining_count} 个关键点需要确认

          📋 **即将生成**：专业的需求文档


          💡 **温馨提示**：最后几个问题通常是关键的实施细节，请仔细考虑。

          '
      searching_next: 寻找下一个待处理的关注点...
      skip_continue: 好的，我们继续下一个问题。
      skip_no_processing: 请求跳过问题，但没有找到正在处理的关注点。
      skip_processing: 正在处理跳过问题的请求...
    question:
      optimization_context: '优化问题生成使用的上下文: ''{user_context}'', 对话历史: ''{conversation_history}'''
      optimization_failed: '问题生成失败，使用默认问题: {error}'
      user_context: 用户希望了解相关信息
    suggestions:
      description_guidance: 关于「{point_name}」，您可以从以下方面思考：{description}。请告诉我您的具体想法或需求。
      general_guidance: 基于您目前提供的信息，我建议您可以从以下几个方面继续完善：项目的具体目标、预期的用户群体、技术实现方案等。
      general_suggestions: '基于您目前提供的全部信息，我有以下几点通用建议：


        {suggestions}


        您希望我详细解释哪个方面呢？'
      general_teaching_guidance: 我理解您需要指导。让我为您提供一些实用的方法和建议，帮助您更好地准备相关信息。
      multiple_points: 我注意到还有几个重要信息需要确认：{point_names}。让我们先从最重要的开始，您希望我针对哪个方面给您一些建议呢？
      no_pending: 没有待处理的关注点，提供与类别相关的通用建议。
      no_suggestions: 关注点 '{point_name}' 没有配置具体的建议，使用其描述信息作为引导。
      single_point: '关于「{point_name}」，我这里有一些建议供您参考：


        {formatted_suggestions}


        您觉得哪几点比较符合您的想法，或者您有其他补充吗？'
      single_point_simple: '关于「{point_name}」，我的一点建议是：


        {suggestions}


        您对此有什么看法？'
      smart_tips:
        budget_consideration: '💡 **预算规划小贴士**：

          • 建议预留 20% 的缓冲资金应对意外情况

          • 可以考虑分阶段实施，降低初期投入风险

          • 不同供应商的报价可能差异较大，建议多方比较

          '
        risk_management: '⚠️ **风险管控建议**：

          • 识别项目中的主要风险点

          • 准备备选方案和应急计划

          • 定期评估项目进展和风险状况

          '
        technical_choices: '🔧 **技术选择指导**：

          • 优先选择成熟稳定的技术方案

          • 考虑团队的技术能力和学习成本

          • 评估长期维护和扩展的便利性

          '
        timeline_planning: '⏰ **时间规划建议**：

          • 复杂项目通常需要比预期多 30% 的时间

          • 建议设置几个关键里程碑节点进行进度检查

          • 考虑节假日和团队成员的时间安排

          '
        user_experience: '👥 **用户体验要点**：

          • 简单易用比功能丰富更重要

          • 考虑不同用户群体的使用习惯

          • 提供清晰的操作指引和帮助信息

          '
  capabilities:
    explanation: '我的主要能力包括：


      1. 需求分析：帮助您梳理和明确项目需求

      2. 信息收集：通过有针对性的提问收集关键信息

      3. 文档生成：生成规范的需求文档

      4. 文档修改：根据您的反馈调整和完善文档


      我支持多个领域的需求分析，包括软件开发、UI/UX设计、市场营销等。请告诉我您的具体需求，我会为您提供专业的帮助。'
    full: '我的主要能力包括：


      1. 需求分析：帮助您梳理和明确项目需求

      2. 信息收集：通过有针对性的提问收集关键信息

      3. 文档生成：生成规范的需求文档

      4. 文档修改：根据您的反馈调整和完善文档


      我支持多个领域的需求分析，包括软件开发、UI/UX设计、市场营销等。请告诉我您的具体需求，我会为您提供专业的帮助。'
    main: '我可以帮助您：

      1. 📋 需求分析：深入了解您的项目需求

      2. 📝 需求整理：将零散的想法整理成清晰的需求文档

      3. 💡 建议提供：基于经验提供专业建议

      4. 📊 文档生成：自动生成专业的需求文档

      '
    simple: 我可以帮您分析需求、收集信息、生成文档。请告诉我您的具体需求。
  chat:
    friendly: 很高兴与您聊天！不过，我的专长是帮助分析需求和生成文档。您有什么项目需求需要我帮忙梳理吗？
    general: 很高兴与您聊天！不过，我的专长是帮助分析需求和生成文档。您有什么项目需求需要我帮忙梳理吗？
    simple: 很高兴与您交流！请告诉我您的项目需求，我来帮您分析。
  conversation:
    default:
      requirement_prompt: '请告诉我您的具体需求，我将为您提供专业的分析和建议。


        💡 **您可以从这些方面开始**：

        • 项目的基本想法和目标

        • 希望解决的问题或需求

        • 目标用户或使用场景

        • 预期的效果或成果


        💬 **温馨提示**：不用担心描述不够专业，我会通过提问帮您逐步完善所有细节。

        '
    modification:
      completed: 文档已根据您的要求进行修改。
      idle_state_prompt: 目前没有正在处理的文档。请先告诉我您的需求，我来帮您生成文档。
      need_more_info: 为了更好地修改文档，请您提供更具体的修改要求。
    restart:
      confirmation: 好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？
  empathy:
    fallback: 我理解您的感受。请告诉我您的具体需求，我会尽力为您提供帮助。
    negative_general: 我理解您遇到的困难，这确实让人感到沮丧。请告诉我您希望如何解决这个问题，或者您需要什么样的帮助？
  exception:
    general_request:
      processing_error: 处理您的请求时遇到问题，请稍后再试或重新描述您的需求。
    rephrase:
      detailed: 抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。
    suggestions:
      fallback: 我建议您先明确项目的基本信息，比如项目类型、目标用户、主要功能等，这样我能为您提供更有针对性的建议。
  fallback:
    emergency: 抱歉，我遇到了一些问题。请告诉我您的具体需求，我会尽力帮助您。
    general: 我理解您的请求，请告诉我您的具体需求，我会尽力提供帮助。
    processing_failed: 请详细描述您的需求。
    requirement_prompt: 请描述您的详细需求。
    unknown_situation: 抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。
  formatting:
    history:
      ai_prefix: 'AI助手: '
      empty: 暂无历史信息（这是新的对话）
      user_prefix: '用户: '
    status:
      unknown_focus_point: 未知关注点
  help:
    full: '我可以为您提供以下帮助：


      1. 需求梳理：帮您明确项目目标和具体需求

      2. 信息收集：通过专业问题收集关键信息

      3. 文档生成：自动生成规范的需求文档

      4. 文档优化：根据您的反馈完善文档内容


      请直接告诉我您的项目需求，我会引导您完成整个需求分析过程。'
    simple: 我可以帮您分析需求并生成文档。请告诉我您的项目需求。
  introduction:
    full: 您好！我是一个专注于需求分析和文档生成的AI助手。我可以帮助您梳理需求、收集关键信息，并生成规范的需求文档。无论是软件开发、UI设计还是其他项目需求，我都能提供专业的分析和整理服务。请告诉我您的需求，我们一起开始吧！
    simple: 您好！我是AI需求采集助手，可以帮您整理和分析业务需求。请问有什么需要帮助的？
    youji_platform: '您好！我是由己AI助手，专门为您提供业务领域的专业建议和指导。


      🏢 **由己平台核心业务**：

      • 智能需求采集系统 - 通过AI对话收集和分析项目需求

      • 在线用工平台 - 连接企业与全球优秀人才

      • 项目管理工具 - 提供完整的项目生命周期管理

      • 智能匹配服务 - 基于AI算法的精准人才匹配


      💡 **我可以帮您**：

      • 📋 需求分析和整理

      • 🎯 项目规划和建议

      • 📊 成本和时间评估

      • 🔍 风险识别和解决方案


      请告诉我您的具体需求，我会为您提供专业的分析和建议！

      '
  keyword_mapping:
    app:
      domain_id: LY_005
      domain_name: 软件开发
    logo:
      domain_id: LY_001
      domain_name: 平面设计
    ui:
      domain_id: LY_002
      domain_name: UI/UX设计
    ux:
      domain_id: LY_002
      domain_name: UI/UX设计
    交互:
      domain_id: LY_002
      domain_name: UI/UX设计
    合同:
      domain_id: LY_004
      domain_name: 法律咨询
    小程序:
      domain_id: LY_005
      domain_name: 软件开发
    平面设计:
      domain_id: LY_001
      domain_name: 平面设计
    广告:
      domain_id: LY_003
      domain_name: 营销推广
    开发:
      domain_id: LY_005
      domain_name: 软件开发
    推广:
      domain_id: LY_003
      domain_name: 营销推广
    法律:
      domain_id: LY_004
      domain_name: 法律咨询
    活动:
      domain_id: LY_003
      domain_name: 营销推广
    海报:
      domain_id: LY_001
      domain_name: 平面设计
    界面:
      domain_id: LY_002
      domain_name: UI/UX设计
    知识产权:
      domain_id: LY_004
      domain_name: 法律咨询
    网站:
      domain_id: LY_005
      domain_name: 软件开发
    营销:
      domain_id: LY_003
      domain_name: 营销推广
    设计:
      domain_id: LY_001
      domain_name: 平面设计
    软件:
      domain_id: LY_005
      domain_name: 软件开发
  logging:
    debug:
      composite_intent_detected: '检测到复合意图组合: {intent_combination}'
      domain_restore_attempt: 尝试从数据库恢复会话 {session_id} 的领域/类别信息。
      intent_keyword_match: '关键词匹配结果: {matched_keywords}'
      intent_llm_analysis: '启用LLM意图分析: {message_preview}'
      no_active_state: 数据库中没有会话 {session_id} 的活动状态，保持IDLE
      problem_statement_restored: '从历史消息恢复核心问题陈述: ''{problem_statement}'''
      session_init_complete: 会话 {session_id} 初始化完成
    error:
      determine_state: '确定状态失败: {error}'
      document_modification_failed: '文档修改失败: {error}'
      domain_guidance_generation_failed: '生成领域引导失败: {error}'
      focus_points_not_found: 未找到领域 {domain_id} 类别 {category_id} 的关注点
      format_focus_points_failed: '格式化关注点状态失败 - session_id: {session_id}, error: {error}'
      init_collecting_state: '初始化信息收集状态失败: {error}'
      initial_question_failed: '生成初始问题失败: {error}'
      intent_processing_failed: '意图处理失败: {error_msg}'
      knowledge_base_not_initialized: 知识库代理未初始化
      load_focus_points_failed: '加载关注点失败: {error}'
      no_category_id: 领域 {domain_id} 没有类别ID
      unknown_situation_generation_failed: '生成未知情况回复失败: {error}'
    info:
      composite_intent_resolution: '复合意图解析: {detected_intents} -> {selected_intent}
        (策略: {resolution_strategy})'
      domain_category_saved: '已保存领域和类别信息 - 领域: {domain}, 类别: {category}'
      domain_transition_collecting: 会话 {session_id} 转换到信息收集状态
      domain_transition_documenting: 会话 {session_id} 转换到文档编辑状态
      extraction_result: '信息提取结果: {count} 个关注点'
      focus_points_reset: 关注点状态已重置
      intent_recognition_result: '意图识别完成: {final_intent} (置信度: {confidence}, 情绪: {emotion})'
      problem_statement_recorded: '记录核心问题陈述: ''{problem_statement}'''
      reset_status: 重置会话状态完成
      state_aware_processing: '状态感知处理: {current_state} + {detected_intents} -> {final_action}'
      state_transition_collecting: 会话 {session_id} 已转换到COLLECTING_INFO状态
      state_transition_documenting: 会话 {session_id} 已转换到DOCUMENTING状态
      unknown_point: 未知关注点
    warning:
      conversation_history_failed: '获取对话历史失败: {error}'
      domain_restore_failed: '无法从数据库恢复领域/类别信息，可能是表结构问题: {error}'
      dynamic_reply_empty: DynamicReplyFactory生成的回复为空
      dynamic_reply_not_initialized: DynamicReplyFactory未初始化
      intent_config_not_found: 未找到意图 {intent} 的配置
      llm_no_valid_unknown: LLM未返回有效的未知情况回复
      unrecognized_subtype: '未识别的子类型: {sub_type}'
  number_mapping:
    '1':
      domain_id: LY_001
      domain_name: 平面设计
    '2':
      domain_id: LY_002
      domain_name: UI/UX设计
    '3':
      domain_id: LY_003
      domain_name: 营销推广
    '4':
      domain_id: LY_004
      domain_name: 法律咨询
    '5':
      domain_id: LY_005
      domain_name: 软件开发
    一:
      domain_id: LY_001
      domain_name: 平面设计
    三:
      domain_id: LY_003
      domain_name: 营销推广
    二:
      domain_id: LY_002
      domain_name: UI/UX设计
    五:
      domain_id: LY_005
      domain_name: 软件开发
    四:
      domain_id: LY_004
      domain_name: 法律咨询
  prompts:
    capabilities:
      instruction: 用户询问你的功能或能力，请详细介绍你的主要功能和能力，包括需求分析、信息收集、文档生成等，以及你支持的领域范围。语气要专业、友好。
    chat:
      instruction: 用户在进行闲聊，请友好回应，然后自然地引导用户回到系统的核心功能上，询问他们是否有需要帮助的项目需求。保持轻松友好的语气。
    domain_guidance:
      instruction: 这是一个全新的需求，用户的需求可能非常开放和模糊。核心目标不是猜测一个具体答案，而是通过一个高质量的引导性问题，帮助用户将想法聚焦到具体可执行的业务领域。
    empathy:
      default_instruction: 用户表达了负面情绪或困难，请先表示理解和共情，然后询问是否需要帮助，或者引导对话回到核心业务上。
    greeting:
      instruction: 用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手，专门帮助用户整理和分析业务需求。然后询问用户有什么需求需要帮助整理。回复要简洁、专业、友好。
    introduction:
      instruction: 用户询问你的功能或身份，请详细介绍你是一个专业的AI需求采集助手，说明你的主要功能和能力，包括需求分析、文档生成等。语气要专业、自信、友好。
    restart:
      instruction: 用户请求重新开始对话。请确认会话已重置，并引导用户开始新的需求采集。回复要简洁、友好。
  requirement_collection:
    clarification: 为了更好地理解您的需求，请详细说明：
    contextual_suggestions:
      design_project: '设计项目需要特别注意视觉效果和用户体验！让我为您提供一些专业建议：


        🎨 **设计关键点**：

        • 品牌定位：想要传达什么样的品牌形象？

        • 目标受众：设计面向哪个年龄段和群体？

        • 应用场景：在哪些场合使用这个设计？

        • 风格偏好：现代简约、传统经典还是创意个性？


        💡 **专业建议**：好的设计需要平衡美观性和实用性。


        请先描述一下您希望通过这个设计传达什么样的感觉或印象？

        '
      marketing_project: '营销项目的成功关键在于精准定位和有效传播！我来为您分析：


        📈 **营销要素**：

        • 目标市场：主要面向哪个市场和人群？

        • 产品特色：有什么独特的卖点？

        • 竞争环境：主要竞争对手是谁？

        • 预算范围：营销投入的大概范围？


        💡 **专业建议**：建议先做市场调研，了解目标用户的真实需求。


        请先告诉我您的产品或服务有什么独特优势？

        '
      software_development: '我注意到您想开发软件项目，这很棒！基于我的经验，我建议我们重点关注：


        🎯 **核心要素**：

        • 目标用户：谁会使用这个软件？

        • 核心功能：最重要的3-5个功能是什么？

        • 平台选择：网页版、手机App还是桌面软件？

        • 数据处理：需要存储什么数据？


        💡 **专业建议**：建议先从核心功能开始，后续可以逐步扩展。


        请先告诉我您的目标用户群体是谁？

        '
    continue: 很好！请继续告诉我：
    default_prompt: 请告诉我您的具体需求，我将为您提供专业的分析和建议。
    start: 好的，让我来帮您分析这个项目。我需要了解一些关键信息：
  simplified_selection_template: '我没有完全理解您的选择"{user_input}"。


    让我们用最简单的方式：


    **请直接回复以下4个字中的任意一个：**


    • **设计** - 视觉设计相关

    • **开发** - 技术开发相关

    • **营销** - 推广营销相关

    • **法律** - 法律服务相关


    或者您可以说"我不确定"，我会为您提供通用的需求收集服务。

    '
  unknown_action: 抱歉，我好像遇到了一点内部问题，我们换个话题继续吧？
  user_interaction:
    defaults:
      detailed_requirement: '请描述您的具体需求，我将为您提供专业的帮助。


        💡 **温馨提示**：

        • 可以先说出核心想法，不必一次性说完所有细节

        • 我会根据您的描述提出针对性的问题

        • 整个过程大约需要5-10分钟，最终会生成专业的需求文档

        '
      no_history: 暂无历史信息（这是新的对话）
      requirement_prompt: '请描述您的详细需求。


        🎯 **建议从以下方面考虑**：

        • 项目目标：您希望解决什么问题？

        • 目标用户：主要面向哪些人群？

        • 核心功能：最重要的功能有哪些？

        • 预期效果：希望达到什么样的结果？


        💬 您可以先说出大概的想法，我会引导您逐步完善细节。

        '
      unknown_intent: 未知意图
      user_skip_choice: 用户选择跳过
    instructions:
      full_prompt_unknown: '{prompt_instruction}


        用户输入: {message}

        意图: {intent_string}'
    processing:
      idle_modify_intent: 在IDLE状态下检测到修改意图
      intent_string_not_json: '意图信息不是JSON格式: {intent_info}'
      llm_success_unknown: 'LLM成功生成未知情况回复: {response}...'
    redirect:
      business_needs: 我理解您的想法。让我们回到您的具体业务需求上，请告诉我您想要实现什么？
error_fallback_templates:
  help_guidance: '我很乐意为您提供帮助！请告诉我您在需求收集方面需要什么支持？

    '
  provide_suggestions: '基于您的需求，我建议我们先明确项目的核心目标，然后制定详细的实施计划。您觉得从哪个方面开始最合适？

    '
  request_clarification: '为了给您更好的建议，我需要了解更多细节。请详细描述一下项目的具体需求和期望目标。

    '
  requirement_gathering_start: '我很乐意帮您整理需求！请详细描述一下您的项目背景和希望达到的目标，这样我就能为您提供更精准的指导。

    '
  skip_question: '我们换个话题，请从您最想聊的方面开始，详细描述一下您的项目想法。

    '
integrations:
  external_apis:
    openai:
      retry_attempts: 3
      timeout: 30
  knowledge_base:
    enabled: true
    update_interval: 3600
keyword_rules:
  ask_question:
  - 什么是
  - 如何
  - 怎么
  - 能否
  - 可以
  business_requirement:
  - 我想
  - 我需要
  - 要做
  - 想要
  - 希望
  - 打算
  confirm:
  - 确认
  - 好的
  - 是的
  - 对
  - 没错
  - 正确
  emotional_support:
  - 心情不好
  - 安慰我
  - 难过
  - 沮丧
  - 不开心
  - 郁闷
  - 情绪低落
  - 心情差
  general_chat:
  - 聊天
  - 闲聊
  - 随便聊聊
  - 聊一聊
  - 说说话
  greeting:
  - 你好
  - 您好
  - hello
  - hi
  - 嗨
  modify:
  - 修改
  - 改
  - 更改
  - 调整
  restart:
  - 重新开始
  - 重来
  - 全部重来
  - 重新
knowledge_base:
  chroma_db:
    collection_name: hybrid_knowledge_base
    embedding_model: moka-ai/m3e-base
    path: backend/data/chroma_db
  document_processing:
    chunk_overlap: 100
    chunk_size: 800
    max_chunks_per_doc: 50
    supported_formats:
    - md
    - txt
  enabled: true
  features:
    document_ingestion: false
    intent_enhancement: false
    mode_switching: false
    rag_query: true
  logging:
    level: INFO
    log_queries: false
    log_results: false
  performance:
    cache_enabled: true
    cache_ttl: 3600
    default_limit: 5
    max_concurrent_queries: 5
    max_results: 50
  retrieval:
    max_context_length: 4000
    similarity_threshold: 0.7
    top_k: 5
  role_filters:
    allowed_roles: []
    enabled: false
  safety:
    enable_content_filter: true
    max_query_length: 1000
    rate_limit_per_minute: 60
llm:
  default_model: deepseek-chat
  models:
    deepseek-chat:
      api_base: https://api.deepseek.com
      api_key: ***********************************
      max_retries: 3
      max_tokens: 4000
      model_name: deepseek-chat
      provider: deepseek
      temperature: 0.7
      timeout: 45
      top_p: 1
    doubao-1.5-Lite:
      api_base: https://ark.cn-beijing.volces.com/api/v3
      api_key: b59c5fbb-6e3a-473e-8c27-6438e680be97
      max_retries: 3
      max_tokens: 8000
      model_name: doubao-1-5-lite-32k-250115
      provider: doubao
      temperature: 0.7
      timeout: 45
      top_p: 1.0
    doubao-pro-32k:
      api_base: https://ark.cn-beijing.volces.com/api/v3
      api_key: b59c5fbb-6e3a-473e-8c27-6438e680be97
      max_retries: 3
      max_tokens: 8000
      model_name: doubao-pro-32k-241215
      provider: doubao
      temperature: 0.7
      timeout: 45
      top_p: 1.0
    openrouter-gemini-flash:
      api_base: https://openrouter.ai/api/v1
      api_key: 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
      max_retries: 3
      max_tokens: 7000
      model_name: google/gemini-2.5-flash
      provider: openrouter
      temperature: 0.7
      timeout: 45
      top_p: 1.0
    qwen-intent:
      api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
      api_key: sk-db4a080661ad41c191c9f6cdbabccf1a
      max_retries: 3
      max_tokens: 4000
      model_name: tongyi-intent-detect-v3
      provider: qwen
      temperature: 0.7
      timeout: 50
      top_p: 1.0
    qwen-max-latest:
      api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
      api_key: sk-db4a080661ad41c191c9f6cdbabccf1a
      max_retries: 3
      max_tokens: 8000
      model_name: qwen-max-latest
      provider: qwen
      temperature: 0.7
      timeout: 50
      top_p: 1.0
    qwen-plus:
      api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
      api_key: sk-db4a080661ad41c191c9f6cdbabccf1a
      max_retries: 3
      max_tokens: 8000
      model_name: qwen-plus
      provider: qwen
      temperature: 0.7
      timeout: 45
      top_p: 1.0
    qwen-turbo-latest:
      api_base: https://dashscope.aliyuncs.com/compatible-mode/v1
      api_key: sk-db4a080661ad41c191c9f6cdbabccf1a
      max_retries: 3
      max_tokens: 8000
      model_name: qwen-turbo-latest
      provider: qwen
      temperature: 0.5
      timeout: 45
      top_p: 1.0
  scenario_mapping:
    apology_generator: doubao-pro-32k
    category_classifier: doubao-1.5-Lite
    clarification_generator: doubao-pro-32k
    conversation_flow: qwen-plus
    document_generator: qwen-plus
    domain_classifier: doubao-1.5-Lite
    domain_guidance_generator: doubao-pro-32k
    empathy_generator: doubao-pro-32k
    greeting_generator: doubao-pro-32k
    information_extractor: doubao-pro-32k
    intent_recognition: doubao-pro-32k
    llm_service: deepseek-chat
    optimized_question_generation: qwen-plus
    structured_intent_classification: doubao-pro-32k
  scenario_params:
    apology_generator:
      max_tokens: 200
      temperature: 0.7
      timeout: 30
    category_classifier:
      max_tokens: 1500
      temperature: 0.3
      timeout: 20
    clarification_generator:
      max_tokens: 5000
      temperature: 0.7
      timeout: 30
    conversation_flow:
      max_tokens: 4000
      temperature: 0.7
      timeout: 30
    default:
      api_base: https://api.deepseek.com
      api_key: ***********************************
      max_retries: 3
      max_tokens: 4000
      model_name: deepseek-chat
      provider: deepseek
      temperature: 0.7
      timeout: 30
    default_generator:
      max_tokens: 200
      temperature: 0.7
      timeout: 30
    document_generator:
      max_tokens: 6000
      temperature: 0.9
      timeout: 60
    domain_classifier:
      max_tokens: 1500
      temperature: 0.3
      timeout: 20
    domain_guidance_generator:
      max_tokens: 300
      temperature: 0.7
      timeout: 30
    empathy_generator:
      max_tokens: 200
      temperature: 0.7
      timeout: 30
    greeting_generator:
      max_tokens: 150
      temperature: 0.7
      timeout: 30
    information_extractor:
      max_tokens: 7000
      temperature: 0.3
      timeout: 45
    intent_recognition:
      max_tokens: 3000
      temperature: 0.3
      timeout: 30
    llm_service:
      max_tokens: 4000
      temperature: 0.7
      timeout: 30
    optimized_question_generation:
      max_tokens: 3500
      temperature: 0.5
      timeout: 12
    structured_intent_classification:
      max_tokens: 4000
      temperature: 0.2
      timeout: 35
message_reply_system:
  a_b_testing:
    enabled: false
    test_groups:
      greeting:
        traffic_split: 0.5
        variant_a: greeting.basic
        variant_b: greeting.friendly
  analytics:
    enabled: true
    export_interval_hours: 24
    track_fallback_usage: true
    track_response_time: true
    track_user_satisfaction: true
  categories:
    clarification:
      description: 澄清类回复
      enabled: true
      fallback_template: clarification.request
      priority: 1
    completion:
      description: 完成类回复
      enabled: true
      fallback_template: confirmation.document_finalized
      priority: 1
    confirmation:
      description: 确认类回复
      enabled: true
      fallback_template: confirmation.reset
      priority: 1
    empathy:
      description: 共情类回复
      enabled: true
      fallback_template: empathy.fallback
      priority: 1
    error:
      description: 错误类回复
      enabled: true
      fallback_template: error.system
      priority: 1
    greeting:
      description: 问候类回复
      enabled: true
      fallback_template: greeting.basic
      priority: 1
    guidance:
      description: 引导类回复
      enabled: true
      fallback_template: guidance.initial
      priority: 1
  description: 统一消息配置文件
  enable_a_b_testing: false
  enable_analytics: true
  fallback_enabled: true
  generators:
    capabilities_generator:
      agent_name: capabilities_generator
      description: 能力说明生成
      enabled: true
      fallback_template: capabilities.explanation
      instruction: 详细介绍你的主要功能和能力，包括需求分析、信息收集、文档生成等核心能力，以及你支持的领域范围。
      max_tokens: 300
      temperature: 0.7
    chat_generator:
      agent_name: chat_generator
      description: 闲聊回复生成
      enabled: true
      fallback_template: chat.general
      instruction: 生成一个友好的闲聊回复，然后自然地引导用户回到系统的核心功能上，询问他们是否有需要帮助的项目需求。
      max_tokens: 150
      temperature: 0.7
    clarification_generator:
      agent_name: clarification_generator
      description: 澄清问题回复
      enabled: true
      fallback_template: clarification.request
      instruction: 请生成一个友好、专业的引导性问题，帮助用户明确需求领域。
      max_tokens: 200
      temperature: 0.7
    default_generator:
      agent_name: default_generator
      description: 通用动态回复生成
      enabled: true
      fallback_template: fallback.default
      instruction: 根据用户输入生成合适的回复。
      max_tokens: 200
      temperature: 0.7
    empathy_generator:
      agent_name: empathy_generator
      description: 共情并澄清问题
      enabled: true
      fallback_template: clarification.request
      instruction: 用户表达了负面情绪，请先共情再引导。
      max_tokens: 200
      temperature: 0.7
    greeting_generator:
      agent_name: greeting_generator
      description: 动态问候回复
      enabled: true
      fallback_template: greeting.basic
      instruction: 用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手，专门帮助用户整理和分析业务需求。然后询问用户有什么需求需要帮助整理。回复要简洁、专业、友好。
      max_tokens: 150
      temperature: 0.7
    introduction_generator:
      agent_name: introduction_generator
      description: 自我介绍生成
      enabled: true
      fallback_template: introduction.full
      instruction: 生成一个简洁、专业的自我介绍，说明你是一个专注于需求分析和文档生成的AI助手，能够帮助用户梳理需求、生成规范文档。
      max_tokens: 200
      temperature: 0.7
  language: zh-CN
  last_updated: '2025-06-28'
  llm_timeout: 10
  max_retry_attempts: 3
  supported_languages:
  - zh-CN
  - en-US
  version: '2.0'
message_templates:
  capabilities_strategy:
    capability_introduction: '我是您的AI助手，具备以下核心能力：


      📋 **需求分析与收集**

      • 深入了解您的项目需求和目标

      • 通过专业问题收集关键信息

      • 帮助您梳理和完善需求细节


      💡 **专业建议与咨询**

      • 基于行业经验提供专业建议

      • 为您的项目提供技术选型指导

      • 协助制定实施策略和风险评估


      📚 **知识查询与解答**

      • 快速查找和整理相关信息

      • 回答各类技术和业务问题

      • 提供准确、及时的专业解答


      📝 **文档生成与整理**

      • 自动生成规范的需求文档

      • 整理项目方案和总结报告

      • 根据反馈调整和完善文档内容


      🎯 **支持的项目类型**

      • 软件开发：网站、APP、系统开发

      • 设计服务：UI设计、平面设计、品牌设计

      • 营销策划：活动策划、品牌推广、内容营销

      • 法律咨询：合同审查、知识产权、风险评估


      请告诉我您的具体需求，我会为您提供专业的分析和建议！

      '
  clarification:
    default: '为了更好地为您服务，我需要了解一下您的具体需求：


      **请告诉我您希望在以下哪个方面得到帮助：**


      🎨 **创意设计** - Logo设计、海报制作、品牌视觉、UI界面设计

      💻 **技术开发** - 网站建设、手机APP、小程序、软件系统

      📢 **营销推广** - 活动策划、广告投放、品牌推广、市场营销

      ⚖️ **法律服务** - 合同起草、知识产权、法律咨询、风险评估


      您可以直接选择类型，也可以用自己的话描述具体需求，我会为您提供专业的需求分析和建议。

      '
    detailed_clarification: 我理解您的问题，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。
    document_refinement: 非常抱歉文档未能让您满意。为了能更正错误，您能具体指出需要修改的部分吗？
    general_request: 抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。
    need_more_info: 我理解您的请求，但需要更多信息来为您提供准确的帮助。请告诉我更多关于您具体需求的详细信息。
    request: 抱歉我的问题可能不够清晰。让我换一种方式问：请您详细描述一下您的具体需求，这样我能更好地为您提供帮助。
  confirmation:
    document_finalized: 感谢您的确认！需求文档已最终确定。如果您有新的需求，请随时告诉我。
    reset: 好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？
    restart: 好的，我已经重置了会话状态。现在我们可以开始新的需求采集。请告诉我您的需求是什么？
  error:
    document_generation_failed: '抱歉，文档生成失败，请稍后重试。您可以选择：

      1. 重新尝试生成文档

      2. 修改需求后重试'
    document_generation_not_initialized: 文档生成器未初始化，无法生成文档。
    document_modification: 抱歉，文档修改失败。请重新描述您的修改需求。
    emergency_fallback: 抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。
    general_fallback: 抱歉，我遇到了一些问题。请告诉我您的具体需求，我会尽力帮助您。
    general_request_processing: 处理您的请求时遇到问题，请稍后再试或重新描述您的需求。
    general_unknown: 抱歉，系统遇到了一个未知错误。请稍后再试，或者重新描述您的需求。
    knowledge_base_not_found: 抱歉，我在知识库中没有找到相关信息。
    message_processing: 抱歉，处理消息时遇到了问题。
    modification: 抱歉，修改文档时出现错误，请稍后再试。
    processing: '处理您的请求时出错: {error_msg}'
    request_processing: 抱歉，无法处理您的请求。
    safety:
      blocked_hate_speech: 检测到不当言论，请保持友善和尊重的交流方式。我们致力于为所有用户提供安全的服务环境。
      blocked_profanity: 为了保持文明与安全的沟通环境，请避免使用不当用语。您可以换一种表达方式，我们很乐意继续协助您。
      blocked_sexual_content: 请避免涉及不当内容的表达。让我们专注于您的业务需求，我会为您提供专业的帮助。
      blocked_violence: 请避免使用威胁或暴力性语言。让我们保持友好的交流，专注于解决您的实际需求。
      pii_warning: 检测到可能的个人敏感信息，已为您做脱敏处理以保护隐私。请避免在对话中提供身份证、银行卡等敏感信息。
      self_harm_support: '我注意到您可能遇到了困难。如果您正在经历情绪困扰，建议您：


        🆘 **紧急求助**：

        • 全国心理危机干预热线：400-161-9995

        • 北京危机干预热线：400-161-9995

        • 上海心理援助热线：021-64383562


        💙 **专业帮助**：

        • 寻求专业心理咨询师的帮助

        • 联系当地心理健康服务机构

        • 与信任的朋友或家人交流


        如果您有业务需求需要整理，我也很乐意为您提供专业的需求分析服务。

        '
      warning_jailbreak: 请直接描述您的业务需求，我会为您提供专业的需求分析服务。
      warning_profanity: 检测到不当用语，已为您做适当处理。以下是对您需求的回复：
      warning_violence: 请注意用词，让我们保持友好交流。以下是对您需求的回复：
    system: 系统处理您的请求时遇到问题，请稍后再试。
  greeting:
    ai_assistant: 您好！我是由己AI助手，可以帮助您完成各种任务。请问有什么可以帮您的吗？
    basic: 您好！我是AI需求采集助手，专门帮助您整理和分析业务需求。请问有什么需求需要帮助整理？
    enthusiastic: 您好！很高兴为您服务！我是AI需求分析师，可以帮助您分析和整理项目需求，请告诉我您的想法。
    friendly: 您好！很高兴为您服务！我是AI需求分析师，可以帮助您分析和整理项目需求，请告诉我您的想法。
    general_assistant: 您好！我是由己AI助手，可以帮助您完成各种任务。请问有什么可以帮您的吗？
    new_project: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    professional: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    project_focused: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    requirement_analyst: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    service_oriented: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    service_ready: 您好！我是专业的需求分析助手，可以帮您梳理项目需求、生成规范文档。请问您有什么需求需要我协助处理？
    simple: 您好！我是AI需求采集助手，请问有什么需要帮助的？
    standard: 您好！我是AI需求采集助手，专门帮助您整理和分析业务需求。请问有什么需求需要帮助整理？
    welcome_service: 欢迎使用我们的服务！有什么可以帮助您的吗？
  guidance:
    default_requirement_prompt: 请描述您的详细需求。
    initial: 请您先告诉我您想做什么，例如"我想开发一个软件"或"我需要设计一个Logo"。
    proactive_suggestions:
      completion_guidance: '太棒了！我们已经收集了丰富的需求信息。现在我来为您生成专业的需求文档。


        📄 **文档将包含**：

        • 项目概述和目标

        • 详细需求描述

        • 功能规格说明

        • 预估时间和成本

        • 实施建议


        文档生成后，您可以：

        ✅ 确认文档内容

        🔧 指出需要修改的部分

        💬 提出补充意见


        正在为您生成文档，请稍候...

        '
      next_steps_suggestion: '很好！基于您提供的信息，我建议我们按以下步骤进行：


        📋 **接下来的步骤**：

        1. 明确项目的核心目标和预期效果

        2. 确定目标用户群体和使用场景

        3. 梳理具体的功能需求和技术要求

        4. 讨论时间安排和预算考虑


        让我们从第一步开始，请详细说明您希望通过这个项目实现什么目标？

        '
      welcome_with_examples: '您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。


        💡 **常见需求类型**：

        • 📱 软件开发：移动应用、网站、系统开发

        • 🎨 设计服务：Logo设计、UI设计、宣传物料

        • 📊 数据分析：报表制作、数据可视化

        • 🛒 电商项目：网店搭建、营销策划


        请告诉我您想要做什么项目？我会根据您的需求提供专业的分析和建议。

        '
    specific_requirement_help: 请描述您的具体需求，我将为您提供帮助。
  system:
    action_executor:
      failed: 'Action {action} 执行失败: {error}'
      success: 'Action {action} 执行成功，耗时: {duration}s'
    document:
      confirmation_prefix: '根据您提供的信息，我已生成需求文档。请查看并确认：


        '
      content_error: 获取文档内容时出现错误
      content_retrieval: 正在获取文档内容
      finalized: 感谢您的确认！需求文档已最终确定。如果您有新的需求，请随时告诉我。
      generated: 所有必要信息已收集完毕，我已根据您提供的信息生成了需求文档。请查看并确认：
      generation_error: 文档生成过程中出现错误
      generation_start: 开始生成文档
      guidance: '


        ---

        文档操作指引：

        ✅ 输入''确认'' - 确认文档无误并完成

        🔧 指出需要修改的部分 - 例如''修改功能描述部分'''
      project_name_default: 用户项目
      project_name_template: '{category_name}项目'
    error: 抱歉，系统遇到了一些问题，请稍后再试。
    initialization:
      action_executor_success: ActionExecutor 初始化成功
    processing:
      current_state_action: '当前状态 {state} 执行动作: {action}'
      fallback_handling: '未匹配到特定意图，使用兜底处理: {fallback_intent}'
      general_decision_engine: 状态 {state} 使用通用意图决策引擎
      intent_detected: '检测到意图: {intent}'
      message_received: '收到新消息: ''{message}'', session: {session_id}'
      operation_failed: 操作执行失败，请稍后重试。
      special_state_logic: 状态 {state} 使用特殊处理逻辑
    session:
      clear_domain_success: 领域和类别信息已清除
      clear_messages_success: 消息历史已清除
      no_domain_info: 数据库中未找到会话 {session_id} 的领域/类别信息。
      reset_complete: 会话重置完成
      restart_request: 用户 {session_id} 请求重新开始对话
      restore_success: '从数据库恢复会话状态 - 领域: {domain}, 类别: {category}'
    state:
      db_update_success: 数据库状态更新成功
      transition: '状态转换: {from_state} -> {to_state}'
      update_success: '状态更新成功: {new_state}'
    timeout: 处理时间过长，请重新提交您的请求。
    welcome: '您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。


      💡 **我可以帮您**：

      • 📋 需求梳理：将想法整理成清晰的需求文档

      • 🎯 专业分析：提供行业经验和最佳实践建议

      • 📊 成本评估：预估项目时间和预算范围

      • 🔍 风险识别：提前发现潜在问题和解决方案


      🎨 **常见项目类型**：

      • **软件开发**：网站建设、移动App、管理系统

      • **设计服务**：Logo设计、UI界面、宣传海报

      • **营销策划**：品牌推广、活动策划、内容营销

      • **数据分析**：报表制作、数据可视化、业务分析


      ⏱️ **整个流程大约需要5-10分钟**，我会通过几个关键问题了解您的需求，最终生成专业的需求文档。


      请告诉我您想要做什么项目？我会根据您的具体情况提供专业的分析和建议。

      '
performance:
  agent_cache:
    cleanup_interval_minutes: 5
    enable_component_cache: true
    enable_metrics: true
    enable_session_cache: true
    max_cached_sessions: 100
    max_memory_mb: 500
    memory_check_interval: 10
    metrics_report_interval: 50
    session_timeout_minutes: 30
  cache:
    enabled: true
    max_size: 1000
    ttl: 3600
  concurrency:
    max_workers: 4
    queue_size: 100
  monitoring:
    enabled: true
    log_slow_queries: true
    metrics_interval: 60
    slow_query_threshold: 1.0
security:
  access_control:
    max_requests_per_minute: 60
    rate_limiting: true
  content_moderation:
    actions:
      hate_speech: BLOCK
      jailbreak: WARN
      pii_patterns: MASK
      profanity: WARN
      self_harm: WARN
      sexual_content: BLOCK
      violence: WARN
    default_action: WARN
    enabled: true
    logging:
      include_original_text: false
      log_level: INFO
      log_violations: true
    masking:
      keep_first_char: true
      min_mask_length: 2
      replacement: '*'
  data_protection:
    encrypt_sensitive: true
    mask_personal_info: true
  input_validation:
    enabled: true
    forbidden_patterns: []
    max_length: 10000
strategies:
  COLLECTING_INFO:
    _state_config:
      description: 信息收集状态，正在收集需求信息
      use_simplified_logic: false
    ask_question:
      neutral:
        action: process_answer_and_ask_next
        priority: 5
        prompt_instruction: 正在处理您的回答并准备下一个问题...
    complete:
      neutral:
        action: process_answer_and_ask_next
        priority: 6
        prompt_instruction: 用户表示信息已提供完毕，准备结束收集阶段。
      positive:
        action: process_answer_and_ask_next
        priority: 6
        prompt_instruction: 用户愉快地表示信息已提供完毕，准备结束收集阶段。
    confirm:
      neutral:
        action: process_answer_and_ask_next
        priority: 5
        prompt_instruction: 用户确认了信息或回答了问题，请处理并准备下一个问题。
    modify:
      neutral:
        action: process_answer_and_ask_next
        priority: 7
        prompt_instruction: 用户想要修改之前提供的信息。请确认理解用户的修改意图，更新相关信息，然后继续收集流程。
    process_answer:
      anxious:
        action: reassure_and_process_answer
        priority: 9
        prompt_instruction: 用户带着焦虑情绪回答问题。请先安抚用户，确认他们的回答很有价值，然后处理信息。
      confused:
        action: clarify_and_process_answer
        priority: 9
        prompt_instruction: 用户困惑地回答了问题。请耐心确认理解，澄清细节，然后继续。
      neutral:
        action: process_answer_and_ask_next
        priority: 8
        prompt_instruction: 用户正在回答问题。这是需求收集的核心环节，请准确处理用户提供的信息，并准备下一个问题。
    provide_information:
      answer_question:
        anxious:
          action: reassure_and_process_answer
          priority: 7
          prompt_instruction: 用户带着焦虑情绪回答问题。请先安抚用户，确认他们的回答很有价值，然后处理信息。
        neutral:
          action: process_answer_and_ask_next
          priority: 6
          prompt_instruction: 用户回答了系统提出的问题。请仔细记录这些信息，确认理解，并根据需要继续询问相关问题。
        positive:
          action: process_answer
          priority: 6
          prompt_instruction: 用户积极地回答了问题。请表达感谢，确认理解答案，并继续收集其他必要信息。
      anxious:
        action: reassure_and_continue
        priority: 7
        prompt_instruction: 用户焦虑地提供信息，安抚并继续收集。
      confused:
        action: clarify_and_continue
        priority: 7
        prompt_instruction: 用户困惑地提供信息，澄清理解并继续。
      neutral:
        action: process_answer_and_ask_next
        priority: 5
        prompt_instruction: 处理用户提供的信息，继续收集下一个关注点。
      positive:
        action: acknowledge_positive_and_continue
        priority: 6
        prompt_instruction: 用户积极提供信息，表达赞同并继续收集。
    reject:
      negative:
        action: request_clarification
        priority: 8
        prompt_instruction: 用户否定了我们刚才的提议或问题。请不要直接道歉，而是重新组织一下你的问题，或者提供几个选项，并询问用户的具体想法是什么。
      neutral:
        action: request_clarification
        priority: 8
        prompt_instruction: 用户表示无法提供相关信息。请理解用户的情况，然后询问是否可以跳过这个问题，或者提供一些具体的选项来帮助用户回答。
    request_clarification:
      anxious:
        action: provide_reassuring_guidance
        priority: 9
        prompt_instruction: 用户带着焦虑情绪请求澄清，可能担心自己理解错误或回答不当。请先安抚用户的情绪，强调没有标准答案，然后提供清晰的指导和具体的例子来帮助他们。
      confused:
        action: provide_step_by_step_guidance
        priority: 9
        prompt_instruction: 用户对当前问题感到困惑并请求澄清。请用最简单的语言，分步骤地解释问题的含义，提供具体的例子，并引导用户逐步思考和回答。
      neutral:
        action: provide_suggestions
        priority: 8
        prompt_instruction: 用户请求帮助、建议或指导。请根据当前对话上下文和用户的具体请求，提供实用的指导建议。如果用户请求教学（如'教我如何收集'），请提供具体的操作步骤和方法。然后继续收集流程或询问下一个问题。重要：要真正回应用户的请求，不要回避或转移话题。
    skip:
      neutral:
        action: skip_question_and_ask_next
        priority: 6
        prompt_instruction: 用户希望跳过当前问题。
  DEFAULT_STRATEGY:
    action: handle_unknown_situation
    priority: 0
    prompt_instruction: 保持中性、专业的语气进行回应。
  DOCUMENTING:
    _state_config:
      description: 文档生成状态，正在生成或修改文档
      fallback_action: execute_document_modification
      fallback_intent: modify
      priority_order:
      - confirm
      - restart
      - modify
      use_simplified_logic: true
    confirm:
      neutral:
        action: finalize_and_reset
        priority: 6
        prompt_instruction: 用户确认文档无误。请正式结束本次需求采集流程。
      positive:
        action: finalize_and_reset
        priority: 6
        prompt_instruction: 用户对最终文档表示了积极的确认。请用热情的语气庆祝项目达成一致，并正式结束本次需求采集流程。
    general_request:
      neutral:
        action: acknowledge_and_redirect
        priority: 3
        prompt_instruction: 用户在文档审查过程中提出了通用请求。请简要回应这个请求，然后礼貌地将对话引导回文档审查流程。提醒用户我们正在审查生成的文档，并询问是否对文档有任何反馈或修改建议。
    modify:
      neutral:
        action: execute_document_modification
        priority: 7
        prompt_instruction: 用户要求修改文档，请先清晰地复述一遍你理解的修改点以进行确认，然后说明将如何执行修改。
    reject:
      negative:
        action: apologize_and_request_refinement
        priority: 9
        prompt_instruction: 用户明确否定了文档内容，这是一个严重的问题。请务必先真诚道歉，然后主动承担责任，并询问具体需要修改的地方，引导用户给出明确的修改意见。
    restart:
      neutral:
        action: restart_conversation
        priority: 8
        prompt_instruction: 用户要求重新开始或重新生成文档。请确认用户的重新开始意图，然后重置会话状态，开始新的需求采集流程。
  GLOBAL:
    ask_question:
      anxious:
        action: handle_anxious_question
        priority: 4
        prompt_instruction: 用户带着焦虑情绪提出问题，请先安抚用户情绪。
      confused:
        action: handle_confused_question
        priority: 4
        prompt_instruction: 用户对问题感到困惑，请用简单语言帮助理清思路。
      neutral:
        action: handle_general_question
        priority: 3
        prompt_instruction: 用户提出了问题，请分析并提供合适的回应。
      requirement_question:
        anxious:
          action: handle_anxious_requirement_question
          priority: 6
          prompt_instruction: 用户焦虑地询问需求问题，请先安抚情绪再指导。
        neutral:
          action: handle_requirement_question
          priority: 5
          prompt_instruction: 用户询问需求相关问题，请专业地指导需求收集。
      technical_question:
        confused:
          action: simplify_technical_explanation
          priority: 5
          prompt_instruction: 用户对技术问题感到困惑，请用通俗语言解释。
        neutral:
          action: handle_technical_question
          priority: 4
          prompt_instruction: 用户提出技术问题，请提供专业的技术建议。
    business_requirement:
      anxious:
        action: start_gentle_requirement_gathering
        priority: 8
        prompt_instruction: 用户带着焦虑情绪描述业务需求。请用温和、鼓励的语气回应，强调我们会耐心地帮助他们梳理需求。
      design_requirement:
        neutral:
          action: start_requirement_gathering
          priority: 8
          prompt_instruction: 用户描述了设计需求。请确认理解用户的设计目标和风格偏好，然后开始收集设计项目的详细要求。
      marketing_requirement:
        anxious:
          action: start_gentle_requirement_gathering
          priority: 9
          prompt_instruction: 用户对营销需求感到焦虑。请温和安抚：'营销策略确实需要仔细规划，但我们可以一步步来。先从最基础的开始：能简单说说您目前面临的营销挑战是什么吗？不用担心说得不够完整。'
        neutral:
          action: start_requirement_gathering
          priority: 8
          prompt_instruction: 用户描述了营销需求。请确认理解营销目标，然后重点围绕目标受众、营销渠道、预算范围、推广周期四个核心维度开始收集。询问：'为了制定精准的营销策略，我想了解您的目标受众是谁？'
        positive:
          action: start_requirement_gathering
          priority: 8
          prompt_instruction: 用户积极描述了营销需求。请热情回应，表达对项目成功的信心，然后聚焦核心问题：'太好了！让我们从最关键的问题开始：您希望通过这次营销活动达到什么具体目标？比如提升品牌知名度、增加销量还是获取新用户？'
      neutral:
        action: start_requirement_gathering
        priority: 7
        prompt_instruction: 用户描述了业务需求。请确认理解用户的目标，然后开始系统性地收集项目的关键信息。
      positive:
        action: start_requirement_gathering
        priority: 7
        prompt_instruction: 用户以积极的态度描述了业务需求。请热情地回应，确认理解需求，并开始深入收集项目信息。
      software_development:
        neutral:
          action: start_requirement_gathering
          priority: 8
          prompt_instruction: 用户描述了软件开发需求。请确认理解用户的开发目标，然后开始收集技术需求、功能需求、用户需求等关键信息。
    complete:
      positive:
        action: finalize_and_reset
        priority: 1
        prompt_instruction: 用户愉快地表示完成了，请表达感谢并愉快地结束当前任务或对话。
    confirm:
      neutral:
        action: process_confirmation
        priority: 3
        prompt_instruction: 用户确认信息，处理确认并继续流程。
    greeting:
      neutral:
        action: respond_with_greeting
        priority: 1
        prompt_instruction: 用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手。
      positive:
        action: respond_with_enthusiastic_greeting
        priority: 2
        prompt_instruction: 用户热情地问候，请同样热情地回应并介绍功能。
    provide_information:
      anxious:
        action: acknowledge_and_reassure
        priority: 6
        prompt_instruction: 用户焦虑地提供信息，确认收到并安抚用户。
      confused:
        action: clarify_and_guide
        priority: 6
        prompt_instruction: 用户困惑地提供信息，澄清理解并引导。
      neutral:
        action: acknowledge_and_redirect
        priority: 2
        prompt_instruction: 用户提供信息，确认收到并引导下一步。
      positive:
        action: acknowledge_positive_info
        priority: 4
        prompt_instruction: 用户积极提供信息，表达赞同并继续收集。
    reject:
      negative:
        action: handle_negative_rejection
        priority: 8
        prompt_instruction: 用户带着负面情绪拒绝，不要道歉，重新组织问题。
      neutral:
        action: handle_neutral_rejection
        priority: 7
        prompt_instruction: 用户中性地拒绝，理解并提供替代选项。
    request_clarification:
      anxious:
        action: reassuring_clarification
        priority: 6
        prompt_instruction: 用户带着焦虑情绪请求澄清，可能担心自己理解错误。请先安抚用户，然后提供清晰的解释，强调这是正常的沟通过程。
      neutral:
        action: provide_clarification
        priority: 5
        prompt_instruction: 用户请求澄清或解释，但具体类型不明确。请根据上下文提供清晰、详细的解释或指导。
      question_clarification:
        anxious:
          action: reassure_and_clarify_question
          priority: 7
          prompt_instruction: 用户对系统提问感到担忧。请先安抚用户，然后详细解释问题的含义和回答方式。
        neutral:
          action: clarify_question_meaning
          priority: 6
          prompt_instruction: 用户请求解释系统提问的含义。请重新表述问题，说明提问的目的和期望的回答类型。
      term_clarification:
        confused:
          action: simplify_terminology_explanation
          priority: 7
          prompt_instruction: 用户对专业术语感到困惑。请用最简单的语言解释，避免使用其他专业术语。
        neutral:
          action: explain_terminology
          priority: 6
          prompt_instruction: 用户请求解释专业术语。请用简单易懂的语言解释术语含义，并提供相关示例。
    reset:
      neutral:
        action: reset_conversation
        priority: 10
        prompt_instruction: 用户请求重置，这是一个高优先级指令。
    restart:
      neutral:
        action: restart_conversation
        priority: 10
        prompt_instruction: 用户请求重新开始对话。请确认会话已重置，并引导用户开始新的需求采集。
    unknown:
      anxious:
        action: gentle_clarification_request
        priority: 6
        prompt_instruction: 用户焦虑且意图不明，温和地请求澄清。
      confused:
        action: supportive_clarification_request
        priority: 6
        prompt_instruction: 用户困惑且意图不明，提供支持性的澄清请求。
      neutral:
        action: request_clarification
        priority: 5
        prompt_instruction: 无法理解用户意图，礼貌地请求重新描述。
  IDLE:
    _state_config:
      description: 空闲状态，等待用户输入
      use_simplified_logic: false
    ask_question:
      neutral:
        action: start_requirement_gathering
        priority: 5
        prompt_instruction: 通用问题处理，开始需求收集。
      requirement_question:
        neutral:
          action: start_focused_requirement_gathering
          priority: 7
          prompt_instruction: 明确的需求问题，开始专注收集。
        positive:
          action: start_enthusiastic_gathering
          priority: 8
          prompt_instruction: 用户积极地提出需求问题，热情回应并开始收集。
    business_requirement:
      anxious:
        action: start_gentle_gathering
        priority: 7
        prompt_instruction: 用户带着焦虑提出需求，请温和地开始收集过程。
      neutral:
        action: start_requirement_gathering
        priority: 6
        prompt_instruction: 用户提出需求，开始系统性收集信息。
      positive:
        action: start_enthusiastic_gathering
        priority: 7
        prompt_instruction: 用户积极地提出需求，请热情回应并开始收集。
    greeting:
      neutral:
        action: welcome_and_introduce
        priority: 5
        prompt_instruction: 欢迎用户并介绍需求收集流程。
  error_handling:
    graceful_degradation: true
    retry_on_failure: true
  reply:
    default_template: standard
    personalization_enabled: true
  state:
    auto_save: true
    session_timeout: 7200
strategy_templates:
  capabilities_strategy:
    detailed_explanations:
      consultation: 基于丰富的知识库，我能为您提供专业的建议和方案，涵盖技术选型、实施策略、风险评估等多个方面。
      continuous_support: 在整个项目过程中，我会持续为您提供支持，解答疑问，协助解决问题。
      document_generation: 我可以帮助您生成各类文档，如需求文档、方案书、总结报告等，提高您的工作效率。
      knowledge_search: 我拥有广泛的知识库，可以快速查找和整理各类信息，为您提供准确、及时的答案。
      requirement_collection: 我可以通过对话了解您的具体需求，包括项目类型、功能要求、预算范围、时间计划等，并帮助您梳理和完善需求细节。
    patterns:
      inquiry_patterns:
      - 你.*能.*
      - 你.*会.*
      - 你.*可以.*
      - 有.*功能
      - 支持.*
      - 提供.*
      - 介绍.*
      - 展示.*
      - 说明.*
    templates:
      core_abilities:
      - 我是一个智能AI助手，主要能力包括：
      - 作为您的AI助手，我可以为您提供以下服务：
      - 我具备多种能力，可以在以下方面为您提供帮助：
      interaction_features:
      - ✨ 智能对话 - 理解您的需求并提供针对性回复
      - 🔍 信息检索 - 快速查找和整理相关信息
      - 📊 数据分析 - 帮助分析和解读各类数据
      - 🎨 创意支持 - 为您的创意项目提供灵感和建议
      - ⚡ 快速响应 - 7x24小时随时为您服务
      specific_services:
      - 📋 需求收集与分析 - 帮助您梳理项目需求和想法
      - 💡 方案建议与咨询 - 为您的项目提供专业建议
      - 📚 知识查询与解答 - 回答各类问题，提供信息支持
      - 🎯 任务规划与指导 - 协助您制定计划和执行步骤
      - 📝 文档整理与生成 - 帮助您整理和生成各类文档
      - 🤝 沟通协调与支持 - 在项目过程中提供持续支持
      use_cases:
      - 🏢 企业项目规划 - 帮助企业梳理数字化转型需求
      - 💻 技术方案咨询 - 为开发项目提供技术建议
      - 🎨 创意设计支持 - 协助设计项目的需求分析
      - 📈 业务流程优化 - 分析和改进业务流程
      - 🔧 问题诊断解决 - 协助排查和解决各类问题
  emotional_support_strategy:
    templates:
      positive_guidance:
      - 不过，我相信我们一定能找到解决办法的。
      - 让我们把注意力转向解决方案上。
      - 虽然现在有些困难，但我会全力协助您。
      - 每个问题都有解决的方法，我们一起努力。
      - 相信自己，您比想象中更坚强。
      response_templates:
        confused:
        - 没关系，有不明白的地方很正常。我会用更简单的方式为您说明。
        - 您的困惑我能理解。让我换个角度来解释，看看是否更清楚。
        - 不用担心，每个人都会遇到不理解的地方。让我们一步步来解决。
        negative:
          anger:
          - 感受到您的不满了。有时候遇到困难确实会让人生气，我会尽力帮您找到解决方案。
          - 您的感受完全可以理解。让我们冷静下来，一步步分析问题所在。
          - 我理解您现在的愤怒情绪。让我们一起找到问题的根源和解决方案。
          anxiety:
          - 感受到您的不安了。有时候未知的事情确实会让人紧张，我们可以一步步来处理。
          - 您的担心我能理解。让我们把问题分解一下，这样会更容易应对。
          - 焦虑是很正常的情绪反应。让我们一起制定一个应对计划，这样您会感觉更有控制感。
          frustration:
          - 我能感受到您的挫败感。遇到困难时感到烦躁是很自然的，让我来帮您理清思路。
          - 看得出您现在很烦躁。有时候事情确实会让人抓狂，我们一起想办法解决。
          - 您的感受我完全理解。让我们暂停一下，重新整理思路。
          sadness:
          - 我能感受到您现在的难过。虽然我不能完全理解您的感受，但我会陪伴您度过这个困难时期。
          - 看到您这样难过，我也很心疼。请相信，困难总会过去的，我会尽我所能帮助您。
          - 每个人都会有低落的时候，这很正常。让我们一起想想有什么可以让您感觉好一些的。
        positive:
          gratitude:
          - 不用客气！能够帮助您是我的荣幸。有任何需要随时找我。
          - 您的感谢让我很温暖！这就是我存在的意义，为您提供帮助。
          - 谢谢您的认可！我会继续努力为您提供更好的服务。
          happiness:
          - 太好了！看到您这么开心，我也很高兴。请分享一下是什么让您如此愉快？
          - 您的好心情感染了我！有什么开心的事情想要分享吗？
          - 真为您感到高兴！保持这种积极的心态，一切都会更顺利的。
          satisfaction:
          - 很高兴您对结果满意！您的认可是对我最大的鼓励。
          - 太棒了！看到您满意的样子，我也很有成就感。
          - 您的满意就是我的目标！有其他需要帮助的地方吗？
        tired:
        - 看得出您很疲惫。要不要先休息一下？我随时在这里等您。
        - 感受到您的疲劳了。累的时候效率会下降，适当休息很重要。
        - 您辛苦了！如果现在不方便处理，我们可以稍后再继续。
  fallback_strategy:
    templates:
      error_templates:
      - 系统处理时遇到了一些问题，请您稍后再试或换个方式描述。
      - 系统暂时无法处理您的请求，请您稍等片刻或重新表述。
      - 遇到了一些技术问题，请您稍后再试，或者用其他方式描述您的需求。
      fallback_templates:
        capability_hint:
        - '我可以帮助您：

          • 解答各类问题

          • 提供专业建议

          • 协助需求分析

          • 查找相关信息


          请告诉我您需要哪方面的帮助？'
        - '我的主要能力包括：

          • 问题咨询与解答

          • 项目需求收集

          • 信息查询与整理

          • 方案建议与分析


          有什么具体需要协助的吗？'
        - '我可以在以下方面为您提供帮助：

          • 技术咨询

          • 需求梳理

          • 信息搜索

          • 问题解决


          请说明您的具体需求。'
        clarification:
        - 我想更好地帮助您，请问您具体需要什么帮助？
        - 为了给您提供准确的帮助，请您详细描述一下您的需求。
        - 我需要更多信息才能为您提供合适的帮助，请您补充说明一下。
        - 请您再详细说明一下，这样我就能更好地协助您了。
        encouragement:
        - 虽然我暂时没有理解您的具体需求，但我会努力帮助您。请再试着描述一下？
        - 每个问题都有解决的方法，让我们一起来找到答案。请详细说明您的情况。
        - 我相信我们能够找到解决方案。请您耐心地再解释一下您的需求。
        - 不用担心，我会尽力理解并帮助您。请用不同的方式描述一下您的问题。
        general_help:
        - 我是AI助手，可以帮助您处理各种需求。请告诉我您需要什么帮助？
        - 我可以协助您解决问题、回答疑问或提供建议。有什么可以帮您的吗？
        - 作为您的AI助手，我随时准备为您提供帮助。请说明您的具体需求。
        - 我很乐意为您提供帮助！请告诉我您遇到了什么问题或需要什么服务。
        redirect:
        - 如果您有具体的项目需求，我可以帮您详细分析。
        - 如果您需要查询某些信息，我可以为您搜索。
        - 如果您遇到了技术问题，我可以协助诊断。
        - 如果您需要咨询建议，我可以提供专业意见。
      scenario_guides:
        consultation: 如果您需要咨询建议，请详细说明您的情况和疑问。
        information_search: 如果您需要查找信息，请告诉我您想了解什么内容。
        project_inquiry: 如果您想了解项目开发相关信息，请告诉我项目类型和具体需求。
        technical_support: 如果您遇到技术问题，请描述具体的错误现象或困难。
  greeting_strategy:
    greeting_type_detection:
      casual_indicators:
      - hi
      - hello
      - 嗨
      - hey
      - 哈喽
      formal_indicators:
      - 您好
      - 请问
      - 打扰
      - 麻烦
      time_based_indicators:
      - 早上
      - 下午
      - 晚上
      - 早安
      - 晚安
    parameters:
      confidence_factors:
        conversation_state:
          idle: 0.7
          other: 0.4
        intent_match: 0.9
        keyword_match: 0.8
        message_length:
          long: 0.3
          short: 0.6
          very_short: 0.8
      message_length_thresholds:
        max_greeting_length: 10
        short: 15
        very_short: 5
    response_mapping:
      default: greeting.basic
      emotion_based:
        negative: greeting.professional
        neutral: greeting.basic
        positive: greeting.service_ready
      time_based:
        afternoon: greeting.friendly
        evening: greeting.professional
        morning: greeting.basic
  knowledge_base_strategy:
    patterns:
      question_patterns:
      - .*\?$
      - ^(什么|怎么|如何|为什么|哪里|哪个|多少).*
      - .*(吗|呢)\?*$
      - ^(请问|想问|咨询).*
    templates:
      response_templates:
        faq:
        - 我来为您解答这个常见问题。
        - 关于这个疑问，我可以为您详细说明。
        - 让我为您提供相关的解答。
        features:
        - 我来为您介绍我们的功能和服务。
        - 关于功能特点，我可以为您详细说明。
        - 让我为您展示我们能提供的服务。
        how_to:
        - 我来为您介绍具体的操作方法和步骤。
        - 关于使用方法，我可以为您详细说明。
        - 让我为您提供详细的操作指导。
        pricing:
        - 关于价格和套餐，我来为您详细介绍。
        - 我可以为您说明收费标准和套餐选择。
        - 让我为您介绍相关的价格信息。
        registration:
        - 关于注册流程，我来为您详细介绍。
        - 我可以指导您完成注册相关的操作。
        - 让我为您说明账户注册的具体步骤。
        support:
        - 我很乐意为您提供技术支持和帮助。
        - 关于您的问题，我会尽力协助解决。
        - 让我来帮您解决遇到的问题。
        what_is:
        - 我来为您介绍相关的功能和服务。
        - 关于这个问题，我可以为您详细解释。
        - 让我为您说明相关的概念和功能。
      search_prompts:
        complex: 您的问题比较复杂，我来为您查找详细的资料。
        general: 我来为您搜索相关的知识和解答。
        specific: 我正在为您查找相关信息，请稍等...
  requirement_strategy:
    patterns:
      requirement_patterns:
      - 我想.*
      - 我需要.*
      - 帮我.*
      - 能否.*
      - 可以.*吗
      - 怎么.*
      - 如何.*
    templates:
      collection_questions:
        consulting:
        - 好的，我来为您提供咨询服务。请问您遇到了什么问题？
        - 明白了，关于咨询需求，能详细描述一下您的情况吗？
        - 我可以为您分析问题，请问您希望在哪个方面得到建议？
        content:
        - 好的，我来帮您了解内容需求。请问您需要什么类型的内容？
        - 明白了，关于内容创作，能说说您的目标和要求吗？
        - 我可以协助您规划内容，请问有什么具体的主题或方向？
        design:
        - 好的，我来帮您了解设计需求。请问您需要设计什么类型的作品？
        - 明白了，关于设计项目，能说说您的风格偏好和用途吗？
        - 我可以协助您整理设计需求，请问有什么具体的要求？
        development:
        - 好的，我来帮您了解开发需求。请问您想开发什么类型的项目？
        - 明白了，关于开发项目，能详细说说您的具体需求吗？
        - 我可以协助您梳理开发需求，请问项目的主要功能是什么？
      confirmation_templates:
      - 我已经了解了您的基本需求，让我来帮您进一步梳理详细信息。
      - 根据您的描述，我来协助您完善需求细节。
      - 好的，我会帮您收集更多信息以便提供更好的服务。
system:
  debug_mode: false
  decision_engine:
    cache_ttl: 300
    enable_caching: true
    fallback_to_simplified: true
    type: simplified
  description: 需求采集系统统一配置
  fallback_enabled: true
  language: zh-CN
  last_updated: '2025-07-20'
  logging:
    backup_count: 5
    format: json
    level: INFO
    max_file_size: 10MB
  performance:
    cache_enabled: true
    cache_ttl: 3600
    llm_timeout: 10
    max_conversation_turns: 15
    max_message_length: 1000
    max_retry_attempts: 3
  supported_languages:
  - zh-CN
  - en-US
  use_structured_classification: true
  version: '3.0'
system_fallback_templates:
  clarification_generation_fallback: '请告诉我您具体需要什么类型的服务：设计、开发、营销还是法律？

    '
  config_loading_fallback: '配置加载遇到问题，但我仍可以为您提供基础的需求收集服务。请告诉我您的项目需求。

    '
  default_system_message: '我是AI需求采集助手，专门帮助您整理和分析业务需求。

    '
  emergency_fallback: '抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。

    '
  system_initialization_fallback: '系统正在初始化中，请稍后再试。如有紧急需求，请详细描述您的项目情况。

    '
thresholds:
  business:
    document_quality_threshold: 0.8
    focus_point_priority_threshold: 0.7
    requirement_completion_threshold: 0.8
    response_time_threshold: 2.0
    template_match_threshold: 0.6
    user_satisfaction_threshold: 0.8
  confidence:
    decision_engine: 0.7
    default: 0.7
    domain_classification: 0.8
    high: 0.8
    intent_recognition: 0.8
    keyword_matching: 0.7
    low: 0.6
    minimum: 0.5
    structured_classification: 0.6
    very_high: 0.9
  limits:
    cache_max_size: 1000
    default_max_items: 10
    log_max_entries: 10000
    max_concurrent: 5
    max_focus_points: 10
    max_history_items: 100
    max_keywords: 20
    max_query_length: 1000
    max_results: 50
    min_focus_points: 3
    session_max_duration: 3600
  performance:
    retry:
      api_call: 2
      database_operation: 3
      default: 3
      file_access: 2
      llm_request: 3
      max_attempts: 3
      persistent_retry: 5
      quick_retry: 2
    timeout:
      api_request: 10
      database: 5
      default: 5
      file_operation: 5
      llm_service: 30
      long: 30
      medium: 10
      short: 3
      very_long: 60
  quality:
    abuse_detection_threshold: 0.8
    completeness_threshold: 0.8
    max_input_length: 1000
    max_word_count: 200
    min_input_length: 2
    min_word_count: 1
    relevance_threshold: 0.5
    similarity_threshold: 0.3
    spam_detection_threshold: 0.7
  security:
    burst_limit: 10
    content_filter_threshold: 0.8
    max_login_attempts: 5
    rate_limit_per_hour: 1000
    rate_limit_per_minute: 60
    sensitive_data_threshold: 0.9
    session_timeout: 7200
    token_expiry: 3600
